/**
 * Games Management Composable
 * Handles games data fetching, CRUD operations
 */

import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { gamesApi } from '@/utils/apis/index.js'
import { extractApiResponse } from '@/utils/helpers/response.helper.js'
import { GAME_STATUS } from '@/utils/configs/constant.config.js'

// Global message tracking to prevent spam
const messageHistory = new Map()
const MESSAGE_COOLDOWN = 2000 // 2 seconds

const preventMessageSpam = (message) => {
  const now = Date.now()
  const lastTime = messageHistory.get(message) || 0

  if (now - lastTime > MESSAGE_COOLDOWN) {
    messageHistory.set(message, now)
    return true
  }
  return false
}

export function useGames() {
  const loading = ref(false)
  const games = ref([])
  const currentGame = ref(null)
  const pagination = ref({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1,
    from: 0,
    to: 0,
    has_more_pages: false
  })

  // Load games list
  const loadGames = async (params = {}) => {
    loading.value = true
    try {
      const response = await gamesApi.getGames(params)
      const result = extractApiResponse(response)
      
      if (result?.success) {
        games.value = result?.data?.data?.map(game => ({
          id: game?.id,
          name: game?.name || '',
          slug: game?.slug || '',
          thumb: game?.thumb || null,
          meta_data: game?.meta_data || null,
          status: game?.status === GAME_STATUS.ACTIVE ? 'active' : 'inactive',
          status_label: game?.status_label || '',
          created_at: game?.created_at || '',
          updated_at: game?.updated_at || '',
          deleted_at: game?.deleted_at || null,
          created_by: game?.created_by || null,
        })) || []
        
        // Update pagination
        if (result?.data?.pagination) {
          pagination.value = {
            current_page: result.data.pagination?.current_page || 1,
            per_page: result.data.pagination?.per_page || 15,
            total: result.data.pagination?.total || 0,
            last_page: result.data.pagination?.last_page || 1,
            from: result.data.pagination?.from || 0,
            to: result.data.pagination?.to || 0,
            has_more_pages: result.data.pagination?.has_more_pages || false
          }
        }
      } else {
        games.value = []
        if (preventMessageSpam('games_load_error')) {
          ElMessage.error(result?.message || 'Không thể lấy danh sách game')
        }
      }
    } catch (error) {
      console.error('Load games error:', error)
      games.value = []
      if (preventMessageSpam('games_load_exception')) {
        ElMessage.error('Có lỗi xảy ra khi tải danh sách game')
      }
    } finally {
      loading.value = false
    }
  }

  // Create new game
  const createGame = async (gameData) => {
    try {
      const response = await gamesApi.createGame(gameData)
      const result = extractApiResponse(response)

      
      if (result?.success) {
        ElMessage.success(result?.message || 'Tạo game mới thành công')
        return result?.data
      } else {
        // Handle validation errors
        if (result?.errors) {
          // Get first error message from errors object
          const firstErrorKey = Object.keys(result.errors)[0]
          const firstErrorMessage = result.errors[firstErrorKey]?.[0]
          ElMessage.error(firstErrorMessage || result?.message || 'Không thể tạo game mới')
        } else {
          ElMessage.error(result?.message || 'Không thể tạo game mới')
        }
        return null
      }
    } catch (error) {
      console.error('Create game error:', error)
      ElMessage.error('Có lỗi xảy ra khi tạo game mới')
      return null
    }
  }

  // Update game
  const updateGame = async (id, gameData) => {
    try {
      const response = await gamesApi.updateGame(id, gameData)
      const result = extractApiResponse(response)

      
      if (result?.success) {
        ElMessage.success(result?.message || 'Cập nhật game thành công')
        return result?.data
      } else {
        if (result?.errors) {
          // Get first error message from errors object
          const firstErrorKey = Object.keys(result.errors)[0]
          const firstErrorMessage = result.errors[firstErrorKey]?.[0]
          ElMessage.error(firstErrorMessage || result?.message || 'Không thể cập nhật game')
        } else {
          ElMessage.error(result?.message || 'Không thể cập nhật game')
        }
        return null
      }
    } catch (error) {
      console.error('Update game error:', error)
      ElMessage.error('Có lỗi xảy ra khi cập nhật game')
      return null
    }
  }

  // Delete game
  const deleteGame = async (id) => {
    try {
      const response = await gamesApi.deleteGame(id)
      const result = extractApiResponse(response)
      
      if (result?.success) {
        ElMessage.success(result?.message || 'Xóa game thành công')
        return true
      } else {
        ElMessage.error(result?.message || 'Không thể xóa game')
        return false
      }
    } catch (error) {
      console.error('Delete game error:', error)
      ElMessage.error('Có lỗi xảy ra khi xóa game')
      return false
    }
  }

  // Update game status
  const updateGameStatus = async (id, status) => {
    try {
      const response = await gamesApi.updateGameStatus(id, status)
      const result = extractApiResponse(response)
      
      if (result?.success) {
        ElMessage.success(result?.message || 'Cập nhật trạng thái game thành công')
        return result?.data
      } else {
        ElMessage.error(result?.message || 'Không thể cập nhật trạng thái game')
        return null
      }
    } catch (error) {
      console.error('Update game status error:', error)
      ElMessage.error('Có lỗi xảy ra khi cập nhật trạng thái game')
      return null
    }
  }

  return {
    loading,
    games,
    currentGame,
    pagination,
    loadGames,
    createGame,
    updateGame,
    deleteGame,
    updateGameStatus,
  }
}
