<template>
  <Modal
    v-model="modalVisible"
    @close="handleClose"
    :title="modalTitle"
    width="500px"
  >
    <div class="p-6">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        @submit.prevent="handleSubmit"
        class="space-y-6"
        label-position="top"
        size="large"
      >
        <!-- Package Type Display -->
        <div class="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900/50">
          <div class="flex items-center justify-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">Loại gói:</span>
            <el-tag 
              :type="packageType === 'recharge' ? 'success' : 'warning'"
              size="large"
            >
              {{ typeLabels[packageType] }}
            </el-tag>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="space-y-6">
          <!-- Name Field -->
          <FormField label="Tên gói" required :error="errors.name">
            <el-form-item prop="name" class="!mb-0">
              <el-input
                v-model="formData.name"
                :placeholder="namePlaceholder"
                :disabled="saving"
                clearable
                @input="handleNameInput"
              />
            </el-form-item>
          </FormField>

          <!-- Value Field -->
          <FormField label="Giá trị (Mcoin)" required :error="errors.value">
            <el-form-item prop="value" class="!mb-0">
              <el-input-number
                v-model="formData.value"
                placeholder="Nhập giá trị Mcoin"
                :disabled="saving"
                :min="1"
                :max="999999"
                :step="1"
                controls-position="right"
                class="w-full"
                @change="handleValueInput"
              />
            </el-form-item>
          </FormField>

          <!-- Status Field -->
          <FormField label="Trạng thái" required :error="errors.status">
            <el-form-item prop="status" class="!mb-0">
              <el-select
                v-model="formData.status"
                placeholder="Chọn trạng thái"
                :disabled="saving"
                class="w-full"
              >
                <el-option label="Hoạt động" value="active" />
                <el-option label="Tạm dừng" value="inactive" />
              </el-select>
            </el-form-item>
          </FormField>

          <!-- Description Field -->
          <FormField label="Mô tả">
            <el-form-item prop="description" class="!mb-0">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                :placeholder="descriptionPlaceholder"
                :disabled="saving"
              />
            </el-form-item>
          </FormField>
        </div>

        <!-- Action Buttons -->
        <ButtonModalCommon
          :loading="saving"
          :can-submit="canSubmit"
          cancel-text="Hủy"
          :submit-text="editingPackage ? 'Cập nhật' : 'Thêm mới'"
          loading-text="Đang lưu..."
          @cancel="handleClose"
          @submit="handleSubmit"
        />
      </el-form>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElTag } from 'element-plus'
import Modal from '@/components/common/Modal.vue'
import FormField from '@/components/common/FormField.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'
import { useFormValidation } from '@/composables/useFormValidation.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editingPackage: {
    type: Object,
    default: null
  },
  packageType: {
    type: String,
    default: 'recharge', // 'recharge' | 'transfer'
    validator: (value) => ['recharge', 'transfer'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'save'])

// Composables
const { formRef, clearAllValidation, validateForm } = useFormValidation()

// State
const saving = ref(false)
const errors = ref({})

// Type labels
const typeLabels = {
  recharge: 'Gói nạp',
  transfer: 'Gói chuyển'
}

// Modal visibility computed property
const modalVisible = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
    if (!value) {
      emit('close')
    }
  }
})

// Modal title
const modalTitle = computed(() => {
  const action = props.editingPackage ? 'Chỉnh sửa' : 'Thêm'
  const packageTypeLabel = typeLabels[props.packageType].toLowerCase()
  return `${action} ${packageTypeLabel}`
})

// Dynamic placeholders
const namePlaceholder = computed(() => {
  const example = props.packageType === 'recharge' ? 'Gói 10 Mcoin' : 'Gói chuyển 10 Mcoin'
  return `Nhập tên gói (VD: ${example})`
})

const descriptionPlaceholder = computed(() => {
  const packageTypeLabel = typeLabels[props.packageType].toLowerCase()
  return `Nhập mô tả về ${packageTypeLabel} (tùy chọn)`
})

// Form data
const formData = reactive({
  name: '',
  value: null,
  status: 'active',
  description: ''
})

// Validation rules
const rules = {
  name: [
    { required: true, message: 'Tên gói là bắt buộc', trigger: 'blur' },
    { min: 3, message: 'Tên gói phải có ít nhất 3 ký tự', trigger: 'change' }
  ],
  value: [
    { required: true, message: 'Giá trị Mcoin là bắt buộc', trigger: 'blur' },
    { type: 'number', min: 1, message: 'Giá trị phải lớn hơn 0', trigger: 'change' }
  ],
  status: [
    { required: true, message: 'Trạng thái là bắt buộc', trigger: 'change' }
  ]
}

// Computed
const canSubmit = computed(() => {
  const nameValid = !!formData.name.trim()
  const valueValid = formData.value && formData.value > 0
  const statusValid = !!formData.status
  
  return nameValid && valueValid && statusValid
})

// Input handlers for real-time validation
const handleNameInput = (value) => {
  formData.name = value
  
  // Clear validation if name is valid
  if (value && value.trim().length >= 3) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('name')
      }
    }, 100)
  }
}

const handleValueInput = (value) => {
  formData.value = value
  
  // Clear validation if value is valid
  if (value && value > 0) {
    setTimeout(() => {
      if (formRef.value) {
        formRef.value.clearValidate('value')
      }
    }, 100)
  }
}

// Watchers
watch(() => props.modelValue, (newShow) => {
  if (newShow) {
    resetForm()
    if (props.editingPackage) {
      loadPackageData()
    }
  }
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    value: null,
    status: 'active',
    description: ''
  })
  
  errors.value = {}
  
  // Clear form validation
  setTimeout(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }, 100)
}

const loadPackageData = () => {
  if (props.editingPackage) {
    Object.assign(formData, {
      name: props.editingPackage.name || '',
      value: props.editingPackage.value || null,
      status: props.editingPackage.status || 'active',
      description: props.editingPackage.description || ''
    })
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  // Validate form
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('Vui lòng kiểm tra lại thông tin')
    return
  }

  saving.value = true
  
  try {
    const packageData = {
      name: formData.name.trim(),
      value: formData.value,
      status: formData.status,
      description: formData.description.trim(),
      type: props.packageType // Include type in saved data
    }

    emit('save', packageData)
    
  } catch (error) {
    ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
// Remove form item margins to use FormField spacing
:deep(.el-form-item) {
  margin-bottom: 0 !important;
}

// Error state styling for form validation
:deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-select .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-input-number .el-input__wrapper) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

// Input number full width styling
:deep(.el-input-number.w-full) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input) {
  width: 100% !important;
}

:deep(.el-input-number.w-full .el-input__wrapper) {
  width: 100% !important;
}
</style>
