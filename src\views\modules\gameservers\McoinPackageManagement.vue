<template>
  <div class="mcoin-package-management-wrapper">
    <PageBreadcrumb 
      :page-title="currentPageTitle" 
      :breadcrumbs="[{ label: 'Quản lý Game Server', to: '/gameservers' }]" 
    />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Type Tabs -->
      <div class="mb-6">
        <el-tabs 
          v-model="selectedType" 
          type="card"
          class="package-type-tabs"
          @tab-change="handleTypeChange"
        >
          <el-tab-pane
            v-for="option in typeOptions"
            :key="option.value"
            :label="option.label"
            :name="option.value"
          />
        </el-tabs>
      </div>

      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              :placeholder="`<PERSON><PERSON><PERSON> kiếm theo tên ${typeLabels[selectedType]}...`"
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>
          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọc theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-3">
            <ButtonCommon type="primary" @click="openAddModal" :icon="PlusIcon">
              {{ `Thêm ${typeLabels[selectedType]}` }}
            </ButtonCommon>
          </div>
        </div>
      </div>

      <!-- Packages Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
        style="overflow-x: auto;"
      >
        <el-table
          v-loading="loading"
          :data="paginatedPackages"
          row-key="id"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="mcoin-package-table"
          :row-class-name="getRowClassName"
          :empty-text="`Chưa có ${typeLabels[selectedType]} nào`"
        >
          <!-- STT Column -->
          <el-table-column label="STT" width="80" align="center">
            <template #default="{ $index }">
              <span class="text-sm text-gray-600">{{ (currentPage - 1) * pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>

          <!-- Type Column -->
          <el-table-column label="Loại gói" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="row.type === 'recharge' ? 'success' : 'warning'"
                size="large"
              >
                {{ typeLabels[row.type] }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- Name Column -->
          <el-table-column label="Tên gói" min-width="100" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2 p-2">
                <span class="font-medium text-gray-900 dark:text-white">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- Value Column -->
          <el-table-column label="Giá trị (Mcoin)" min-width="200" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="row.type === 'recharge' ? 'success' : 'warning'"
                size="large"
              >
                {{ row.value }} Mcoin
              </el-tag>
            </template>
          </el-table-column>

          <!-- Status Column -->
          <el-table-column label="Trạng thái" width="200" align="center">
            <template #default="{ row }">
              <el-switch
                :model-value="row.status === 'active'"
                @change="handleStatusChange(row, $event)"
                :active-value="true"
                :inactive-value="false"
              />
            </template>
          </el-table-column>

          <!-- Created Date Column -->
          <el-table-column label="Ngày tạo" width="150" align="center">
            <template #default="{ row }">
              <div class="text-center text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(row.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- Actions Column -->
          <el-table-column label="HÀNH ĐỘNG" min-width="100" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2">
                <ActionButtons
                  :show-view="false"
                  :show-edit="true"
                  :show-delete="true"
                  @edit="openEditModal(row)"
                  @delete="confirmDelete(row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
    </div>

    <!-- Package Modal -->
    <McoinPackageModal
      v-model="showModal"
      :editing-package="editingPackage"
      :package-type="selectedType"
      @close="closeModal"
      @save="handleSavePackage"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage, ElTabs, ElTabPane } from 'element-plus'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import Pagination from '@/components/common/Pagination.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import McoinPackageModal from '@/components/modules/gameservers/McoinPackageModal.vue'
import { PlusIcon } from '@/components/icons/index.js'

// Icons
const SearchIcon = Search

// Type definitions
const typeOptions = [
  { label: 'Gói nạp', value: 'recharge' },
  { label: 'Gói chuyển', value: 'transfer' }
]

const typeLabels = {
  recharge: 'gói nạp',
  transfer: 'gói chuyển'
}

// State
const selectedType = ref('recharge')
const loading = ref(false)
const mcoinPackages = ref([])
const showModal = ref(false)
const editingPackage = ref(null)

// Filter states
const searchTerm = ref('')
const selectedStatus = ref('')

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)

// Computed
const currentPageTitle = computed(() => {
  return selectedType.value === 'recharge' 
    ? 'Quản lý gói nạp Mcoin' 
    : 'Quản lý gói chuyển Mcoin vào game'
})

const filteredPackages = computed(() => {
  let filtered = mcoinPackages.value.filter(pkg => pkg.type === selectedType.value)

  // Filter by search term
  if (searchTerm.value.trim()) {
    const search = searchTerm.value.toLowerCase().trim()
    filtered = filtered.filter(pkg =>
      pkg.name.toLowerCase().includes(search)
    )
  }

  // Filter by status
  if (selectedStatus.value) {
    filtered = filtered.filter(pkg => pkg.status === selectedStatus.value)
  }

  return filtered
})

const paginatedPackages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPackages.value.slice(start, end)
})

const pagination = computed(() => ({
  current_page: currentPage.value,
  per_page: pageSize.value,
  total: filteredPackages.value.length,
  from: filteredPackages.value.length === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1,
  to: Math.min(currentPage.value * pageSize.value, filteredPackages.value.length),
}))

// Status options
const statusOptions = [
  { label: 'Hoạt động', value: 'active' },
  { label: 'Tạm dừng', value: 'inactive' }
]

// Sample data - Combined data with type field
const samplePackages = [
  // Recharge packages
  {
    id: 1,
    name: 'Gói 10 Mcoin',
    value: 10,
    type: 'recharge',
    status: 'active',
    created_at: '2025-08-21'
  },
  {
    id: 2,
    name: 'Gói 20 Mcoin',
    value: 20,
    type: 'recharge',
    status: 'inactive',
    created_at: '2025-08-22'
  },
  {
    id: 3,
    name: 'Gói 50 Mcoin',
    value: 50,
    type: 'recharge',
    status: 'active',
    created_at: '2025-08-20'
  },
  // Transfer packages
  {
    id: 4,
    name: 'Gói chuyển 10 Mcoin',
    value: 10,
    type: 'transfer',
    status: 'active',
    created_at: '2025-08-21'
  },
  {
    id: 5,
    name: 'Gói chuyển 20 Mcoin',
    value: 20,
    type: 'transfer',
    status: 'inactive',
    created_at: '2025-08-22'
  },
  {
    id: 6,
    name: 'Gói chuyển 50 Mcoin',
    value: 50,
    type: 'transfer',
    status: 'active',
    created_at: '2025-08-20'
  }
]

// Table styling
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    fontSize: '14px',
    padding: '16px 12px',
    textAlign: 'center',
    height: '48px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderColor: 'var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const loadPackages = async () => {
  loading.value = true
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000))
  mcoinPackages.value = samplePackages
  loading.value = false
}

const handleTypeChange = () => {
  // Reset filters when changing type
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const handleStatusFilter = async () => {
  currentPage.value = 1 // Reset to first page when filtering
}

const handleResetFilters = async () => {
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
}

const handlePerPageChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

const handleStatusChange = async (pkg, isActive) => {
  try {
    const newStatus = isActive ? 'active' : 'inactive'
    const packageIndex = mcoinPackages.value.findIndex(p => p.id === pkg.id)
    if (packageIndex !== -1) {
      mcoinPackages.value[packageIndex].status = newStatus
      ElMessage.success(`Đã cập nhật trạng thái ${typeLabels[pkg.type]} "${pkg.name}"`)
    }
  } catch (error) {
    ElMessage.error('Có lỗi xảy ra khi cập nhật trạng thái')
  }
}

const openAddModal = () => {
  editingPackage.value = null
  showModal.value = true
}

const openEditModal = (pkg) => {
  editingPackage.value = pkg
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingPackage.value = null
}

const handleSavePackage = (packageData) => {
  try {
    if (editingPackage.value) {
      // Update existing package
      const index = mcoinPackages.value.findIndex(p => p.id === editingPackage.value.id)
      if (index !== -1) {
        mcoinPackages.value[index] = {
          ...mcoinPackages.value[index],
          ...packageData,
          type: selectedType.value // Ensure type is maintained
        }
        ElMessage.success(`Cập nhật ${typeLabels[selectedType.value]} thành công!`)
      }
    } else {
      // Add new package
      const newPackage = {
        id: Date.now(),
        ...packageData,
        type: selectedType.value,
        created_at: new Date().toISOString().split('T')[0]
      }
      mcoinPackages.value.push(newPackage)
      ElMessage.success(`Thêm ${typeLabels[selectedType.value]} mới thành công!`)
    }

    closeModal()
  } catch (error) {
    ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
  }
}

const confirmDelete = async (pkg) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa ${typeLabels[pkg.type]} "${pkg.name}" không?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const index = mcoinPackages.value.findIndex(p => p.id === pkg.id)
    if (index !== -1) {
      mcoinPackages.value.splice(index, 1)
      ElMessage.success(`Xóa ${typeLabels[pkg.type]} thành công!`)
    }
  } catch {
    // User cancelled
  }
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    currentPage.value = 1 // Reset to first page when searching
  }, 300)
})

// Lifecycle
onMounted(() => {
  loadPackages()
})
</script>

<style lang="scss" scoped>
/* Enhanced table styling */
:deep(.mcoin-package-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mcoin-package-table .even-row) {
  background-color: var(--el-bg-color);
}

:deep(.mcoin-package-table .odd-row) {
  background-color: var(--el-fill-color-light);
}

:deep(.mcoin-package-table .even-row:hover),
:deep(.mcoin-package-table .odd-row:hover) {
  background-color: var(--el-fill-color) !important;
}

:deep(.mcoin-package-table .el-table__header-wrapper) {
  background-color: var(--el-bg-color-page);
}

:deep(.mcoin-package-table .el-table__row) {
  transition: background-color 0.2s ease;
  height: 56px !important;
  min-height: 56px !important;
}

:deep(.mcoin-package-table .el-table__cell) {
  border-color: var(--el-border-color-lighter);
  vertical-align: middle;
}

/* Ensure all table headers have consistent styling */
:deep(.mcoin-package-table .el-table__header .el-table__cell) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 14px !important;
  padding: 12px !important;
  border-bottom: 1px solid var(--el-border-color-light) !important;
  text-align: center !important;
  height: 48px !important;
}

/* Ensure all table body cells have consistent styling */
:deep(.mcoin-package-table .el-table__body .el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  padding: 12px !important;
  font-size: 14px !important;
  height: 56px !important;
  vertical-align: middle !important;
}

/* Table empty state styling */
:deep(.mcoin-package-table .el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

:deep(.mcoin-package-table .el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode support */
.dark :deep(.mcoin-package-table .el-table__empty-block) {
  background-color: #1e293b;
}

.dark :deep(.mcoin-package-table .el-table__empty-text) {
  color: #94a3b8;
}

/* Switch styling */
:deep(.mcoin-package-table .el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* Segmented control styling */
:deep(.el-segmented) {
  --el-segmented-bg-color: var(--el-bg-color);
  --el-segmented-item-selected-bg-color: var(--el-color-primary);
  --el-segmented-item-selected-color: #ffffff;
}
</style>
