// Test file để kiểm tra mapping data response mới
// <PERSON><PERSON><PERSON> là response mẫu từ server
const mockResponse = {
  "success": true,
  "message": "Lấy danh sách game thành công.",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "<PERSON><PERSON><PERSON> hiệp tình",
        "thumb": null,
        "slug": "kiem-hiep-tinh",
        "meta_data": null,
        "status": {
          "value": 1,
          "label": "Đang hoạt động"
        },
        "created_at": "2025-08-27 11:02:45",
        "updated_at": "2025-08-27 11:02:45",
        "deleted_at": null,
        "created_by": null,
        "servers": [
          {
            "id": 1,
            "name": "S1 - Gà Mới",
            "tag": "NORMAL",
            "status": {
              "value": 3,
              "label": "Bảo trì"
            },
            "is_online": false
          },
          {
            "id": 2,
            "name": "S2 - Gà Chọi",
            "tag": "NORMAL",
            "status": {
              "value": 3,
              "label": "Bảo trì"
            },
            "is_online": false
          },
          {
            "id": 3,
            "name": "S3 - Gà Già",
            "tag": "NORMAL",
            "status": {
              "value": 3,
              "label": "Bảo trì"
            },
            "is_online": false
          }
        ]
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 15,
      "total": 1,
      "last_page": 1,
      "from": 1,
      "to": 1,
      "has_more_pages": false
    }
  }
}

// Test mapping function
import { GAME_STATUS, SERVER_STATUS } from './src/utils/configs/constant.config.js'

const getServerStatusString = (statusValue) => {
  switch (statusValue) {
    case SERVER_STATUS.ACTIVE:
      return 'active'
    case SERVER_STATUS.MAINTENANCE:
      return 'maintenance'
    case SERVER_STATUS.INACTIVE:
    default:
      return 'inactive'
  }
}

// Test mapping
const mappedGames = mockResponse.data.data.map(game => ({
  id: game?.id,
  name: game?.name || '',
  slug: game?.slug || '',
  thumb: game?.thumb || null,
  meta_data: game?.meta_data || null,
  status: game?.status?.value === GAME_STATUS.ACTIVE ? 'active' : 'inactive',
  status_label: game?.status?.label || '',
  created_at: game?.created_at || '',
  updated_at: game?.updated_at || '',
  deleted_at: game?.deleted_at || null,
  created_by: game?.created_by || null,
  servers: game?.servers?.map(server => ({
    id: server?.id,
    name: server?.name || '',
    tag: server?.tag || '',
    status: getServerStatusString(server?.status?.value),
    status_label: server?.status?.label || '',
    is_online: server?.is_online || false,
  })) || [],
}))

console.log('Mapped games:', JSON.stringify(mappedGames, null, 2))

// Expected output:
// [
//   {
//     "id": 1,
//     "name": "Kiếm hiệp tình",
//     "slug": "kiem-hiep-tinh",
//     "thumb": null,
//     "meta_data": null,
//     "status": "active",
//     "status_label": "Đang hoạt động",
//     "created_at": "2025-08-27 11:02:45",
//     "updated_at": "2025-08-27 11:02:45",
//     "deleted_at": null,
//     "created_by": null,
//     "servers": [
//       {
//         "id": 1,
//         "name": "S1 - Gà Mới",
//         "tag": "NORMAL",
//         "status": "maintenance",
//         "status_label": "Bảo trì",
//         "is_online": false
//       },
//       {
//         "id": 2,
//         "name": "S2 - Gà Chọi",
//         "tag": "NORMAL",
//         "status": "maintenance",
//         "status_label": "Bảo trì",
//         "is_online": false
//       },
//       {
//         "id": 3,
//         "name": "S3 - Gà Già",
//         "tag": "NORMAL",
//         "status": "maintenance",
//         "status_label": "Bảo trì",
//         "is_online": false
//       }
//     ]
//   }
// ]
