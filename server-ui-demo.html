<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Server UI</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Demo Giao Diện Server Mới</h1>
        
        <!-- Game Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold mb-4">Danh sách Server của "Kiếm hiệp tình"</h2>
            
            <!-- Server Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                
                <!-- Server 1 - Active & Online -->
                <div class="server-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
                    <!-- Server Header -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-xs font-medium text-blue-600">#1</span>
                            </div>
                            <div class="flex flex-col">
                                <h5 class="text-sm font-semibold text-gray-900 truncate">
                                    S1 - Gà Mới
                                </h5>
                                <span class="text-xs text-gray-500">NORMAL</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Server Status Icons -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <!-- Status Icon -->
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                <span class="text-xs font-medium text-green-600">Hoạt động</span>
                            </div>
                            
                            <!-- Online Status Icon -->
                            <div class="flex items-center gap-1 ml-2">
                                <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                <span class="text-xs text-green-600">Online</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Server 2 - Maintenance & Offline -->
                <div class="server-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
                    <!-- Server Header -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-xs font-medium text-blue-600">#2</span>
                            </div>
                            <div class="flex flex-col">
                                <h5 class="text-sm font-semibold text-gray-900 truncate">
                                    S2 - Gà Chọi
                                </h5>
                                <span class="text-xs text-gray-500">NORMAL</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Server Status Icons -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <!-- Status Icon -->
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                                <span class="text-xs font-medium text-yellow-600">Bảo trì</span>
                            </div>
                            
                            <!-- Online Status Icon -->
                            <div class="flex items-center gap-1 ml-2">
                                <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                                <span class="text-xs text-gray-500">Offline</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Server 3 - Inactive & Offline -->
                <div class="server-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
                    <!-- Server Header -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-xs font-medium text-blue-600">#3</span>
                            </div>
                            <div class="flex flex-col">
                                <h5 class="text-sm font-semibold text-gray-900 truncate">
                                    S3 - Gà Già
                                </h5>
                                <span class="text-xs text-gray-500">NORMAL</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Server Status Icons -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <!-- Status Icon -->
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                                <span class="text-xs font-medium text-red-600">Không hoạt động</span>
                            </div>
                            
                            <!-- Online Status Icon -->
                            <div class="flex items-center gap-1 ml-2">
                                <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                                <span class="text-xs text-gray-500">Offline</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Server 4 - Active & Offline -->
                <div class="server-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
                    <!-- Server Header -->
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-xs font-medium text-blue-600">#4</span>
                            </div>
                            <div class="flex flex-col">
                                <h5 class="text-sm font-semibold text-gray-900 truncate">
                                    S4 - Test Server
                                </h5>
                                <span class="text-xs text-gray-500">VIP</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Server Status Icons -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <!-- Status Icon -->
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                <span class="text-xs font-medium text-green-600">Hoạt động</span>
                            </div>
                            
                            <!-- Online Status Icon -->
                            <div class="flex items-center gap-1 ml-2">
                                <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                                <span class="text-xs text-gray-500">Offline</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 class="text-sm font-semibold mb-3">Chú thích:</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <h4 class="text-xs font-medium text-gray-700 mb-2">Trạng thái Server:</h4>
                    <div class="space-y-1">
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-green-500"></div>
                            <span class="text-xs text-green-600">Hoạt động</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                            <span class="text-xs text-yellow-600">Bảo trì</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-red-500"></div>
                            <span class="text-xs text-red-600">Không hoạt động</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-xs font-medium text-gray-700 mb-2">Kết nối:</h4>
                    <div class="space-y-1">
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-green-500"></div>
                            <span class="text-xs text-green-600">Online</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                            <span class="text-xs text-gray-500">Offline</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
