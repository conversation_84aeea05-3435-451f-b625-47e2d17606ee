<template>
  <div class="game-management-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý Game Server', to: '/gameservers' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              placeholder="Tìm kiếm theo tên game..."
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>
          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọc theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <!-- <div class="ml-auto flex items-center gap-3">
            <ButtonCommon type="primary" @click="openAddModal" :icon="PlusIcon">
              Thêm game mới
            </ButtonCommon>
          </div> -->
        </div>
      </div>

      <!-- Games Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
        style="overflow-x: auto;"
      >
        <el-table
          v-loading="loading"
          :data="paginatedGames"
          row-key="id"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="games-table"
          :row-class-name="getRowClassName"
          empty-text="Chưa có game nào"
          :expand-row-keys="Array.from(expandedRows)"
          @expand-change="handleExpandChange"
          ref="gamesTable"
        >
          <!-- Expand Column -->
          <el-table-column type="expand" width="50" align="center" label="">
            <template #default="{ row }">
              <div class="expanded-content p-4 bg-blue-50 dark:bg-gray-800">
                <!-- Header with title and sync button -->
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Danh sách Server của "{{ row?.name || '' }}"
                  </h4>
                </div>
                
                <!-- Server Cards Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  <div 
                    v-for="server in row?.servers || []" 
                    :key="server?.id"
                    class="server-card bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4 hover:shadow-md transition-shadow duration-200"
                  >
                    <!-- Server Header -->
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <span class="text-xs font-medium text-blue-600 dark:text-blue-400">#{{ server?.id }}</span>
                        </div>
                        <div class="flex flex-col">
                          <h5 class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                            {{ server?.name || '' }}
                          </h5>
                          <span v-if="server?.tag" class="text-xs text-gray-500 dark:text-gray-400">
                            {{ server.tag }}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Server Status Icons -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <!-- Status Icon -->
                        <div class="flex items-center gap-1">
                          <div
                            :class="[
                              'w-2 h-2 rounded-full',
                              getServerStatusIconClass(server?.status)
                            ]"
                          ></div>
                          <span class="text-xs font-medium" :class="getServerStatusTextClass(server?.status)">
                            {{ server?.status_label || getServerStatusLabel(server?.status) }}
                          </span>
                        </div>

                        <!-- Online Status Icon -->
                        <div class="flex items-center gap-1 ml-2">
                          <div
                            :class="[
                              'w-2 h-2 rounded-full',
                              server?.is_online ? 'bg-green-500' : 'bg-gray-400'
                            ]"
                          ></div>
                          <span class="text-xs" :class="server?.is_online ? 'text-green-600' : 'text-gray-500'">
                            {{ server?.is_online ? 'Online' : 'Offline' }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Empty State -->
                <div 
                  v-if="!row?.servers || row.servers.length === 0" 
                  class="text-center py-8"
                >
                  <div class="text-gray-400 dark:text-gray-500 text-sm">
                    Chưa có server nào cho game này
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- STT Column -->
          <el-table-column label="STT" width="80" align="center">
            <template #default="{ $index }">
              <span class="text-sm text-gray-600">{{ (currentPage - 1) * pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>

          <!-- Name Column -->
          <el-table-column label="Tên game" min-width="200" align="center">
            <template #default="{ row }">
              <div class="flex items-center gap-2 p-2">
                <!-- Game Thumbnail -->
                <div class="flex-shrink-0">
                  <div class="w-20 h-14 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-100 game-thumbnail shadow-sm">
                    <img 
                      v-if="row?.thumb" 
                      :src="row.thumb" 
                      :alt="row?.name || ''"
                      class="w-full h-full object-cover"
                      @error="handleImageError"
                    />
                    <div 
                      v-else 
                      class="w-full h-full flex items-center justify-center text-gray-600 font-semibold text-sm bg-gray-50"
                    >
                      {{ getGameInitials(row?.name || '') }}
                    </div>
                  </div>
                </div>
                <!-- Game Name -->
                <div class="flex-1 min-w-0">
                  <span class="font-medium text-gray-900 dark:text-white truncate block text-left">{{ row?.name || '' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- Slug Column -->
          <el-table-column label="Slug" width="200" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row?.slug || '' }}</el-tag>
            </template>
          </el-table-column>

          <!-- Status Column -->
          <el-table-column label="Trạng thái" width="200" align="center">
            <template #default="{ row }">
              <el-switch
                :model-value="row?.status === 'active'"
                @change="handleStatusChange(row, $event)"
                :active-value="true"
                :inactive-value="false"
              />
            </template>
          </el-table-column>

          <!-- Created Date Column -->
          <el-table-column label="Ngày tạo" width="200" align="center">
            <template #default="{ row }">
              <div class="text-center text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(row?.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- Actions Column -->
          <el-table-column label="THAO TÁC" min-width="150" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2">
                <ActionButtons
                  :show-view="false"
                  :show-edit="true"
                  :show-delete="true"
                  @edit="openEditModal(row)"
                  @delete="confirmDelete(row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
    </div>


    <!-- Game Modal -->
    <GameModal
      v-model="showModal"
      :editing-game="editingGame"
      @close="closeModal"
      @save="handleSaveGame"
    />

    <!-- Meta Data Modal -->
    <MetaDataModal
      v-model="showMetaModal"
      :meta-data="selectedMetaData"
      :server-name="selectedServerName"
      @close="showMetaModal = false"
      @edit="handleEditMetaData"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import Pagination from '@/components/common/Pagination.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import GameModal from '@/components/modules/gameservers/GameModal.vue'
import MetaDataModal from '@/components/modules/gameservers/MetaDataModal.vue'
import { PlusIcon, EyeIcon } from '@/components/icons/index.js'
import { useGames } from '@/composables/useGames.js'

// Icons
const SearchIcon = Search

// Page title
const currentPageTitle = ref('Quản lý Game')

// Composables
const {
  loading,
  games,
  pagination: apiPagination,
  loadGames,
  createGame,
  updateGame,
  deleteGame,
  updateGameStatus,
  loadGameServers,
} = useGames()

// State
const expandedRows = ref(new Set())
const showModal = ref(false)
const showMetaModal = ref(false)
const editingGame = ref(null)
const selectedMetaData = ref({})
const selectedServerName = ref('')
const gamesTable = ref(null)


// Filter states
const searchTerm = ref('')
const selectedStatus = ref('')

// Pagination
const currentPage = ref(1)
const pageSize = ref(15)

// Local pagination computed (for filtering)
const pagination = computed(() => ({
  current_page: currentPage.value,
  per_page: pageSize.value,
  total: filteredGames.value.length,
  from: filteredGames.value.length === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1,
  to: Math.min(currentPage.value * pageSize.value, filteredGames.value.length),
}))

// Status options
const statusOptions = [
  { label: 'Hoạt động', value: 'active' },
  { label: 'Tạm dừng', value: 'inactive' }
]

// Computed
const filteredGames = computed(() => {
  let filtered = games.value

  // Filter by search term
  if (searchTerm.value.trim()) {
    const search = searchTerm.value.toLowerCase().trim()
    filtered = filtered.filter(game =>
      game?.name?.toLowerCase().includes(search) ||
      game?.slug?.toLowerCase().includes(search)
    )
  }

  // Filter by status
  if (selectedStatus.value) {
    filtered = filtered.filter(game => game?.status === selectedStatus.value)
  }

  return filtered
})

const paginatedGames = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredGames.value.slice(start, end)
})

// Enhanced table styles based on CategoryList
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: '14px',
    padding: '16px 12px',
    borderBottom: '1px solid var(--el-border-color-light)',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderBottom: '1px solid var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const loadGamesData = async (params = {}) => {
  const searchParams = {
    page: currentPage.value,
    per_page: pageSize.value,
    ...params
  }
  
  if (searchTerm.value.trim()) {
    searchParams.search = searchTerm.value.trim()
  }
  
  if (selectedStatus.value) {
    searchParams.status = selectedStatus.value === 'active' ? 1 : 0
  }
  
  await loadGames(searchParams)
}

const toggleExpand = (gameId) => {
  if (expandedRows.value.has(gameId)) {
    expandedRows.value.delete(gameId)
  } else {
    expandedRows.value.add(gameId)
  }
}

const handleExpandChange = async (row, expandedRowsList) => {
  expandedRows.value = new Set(expandedRowsList.map(r => r.id))
  
  // Load servers when expanding a row
  if (expandedRowsList.some(r => r.id === row.id)) {
    try {
      const servers = await loadGameServers(row.id)
      // Update the game with servers data
      const gameIndex = games.value.findIndex(g => g.id === row.id)
      if (gameIndex !== -1) {
        games.value[gameIndex].servers = servers
      }
    } catch (error) {
      console.error('Error loading servers:', error)
    }
  }
  
  // Add CSS class to track expanded state for icon rotation
  setTimeout(() => {
    const tableEl = document.querySelector('.games-table')
    if (tableEl) {
      // Remove all expanded classes first
      tableEl.querySelectorAll('.el-table__row').forEach(row => {
        row.classList.remove('expanded')
      })
      
      // Add expanded class to currently expanded rows
      expandedRowsList.forEach(expandedRow => {
        const rowEl = tableEl.querySelector(`[data-row-key="${expandedRow.id}"]`)
        if (rowEl) {
          rowEl.classList.add('expanded')
        }
      })
    }
  }, 50)
}

const handleStatusFilter = async () => {
  currentPage.value = 1 // Reset to first page when filtering
  await loadGamesData()
}

const handleResetFilters = async () => {
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
  await loadGamesData()
}

const handlePageChange = async (page) => {
  currentPage.value = page
  await loadGamesData()
}

const handlePerPageChange = async (size) => {
  pageSize.value = size
  currentPage.value = 1
  await loadGamesData()
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

const handleStatusChange = async (game, isActive) => {
  try {
    const newStatus = isActive ? 1 : 0
    const result = await updateGameStatus(game.id, newStatus)
    
    if (result) {
      // Update local game status
      const gameIndex = games.value.findIndex(g => g.id === game.id)
      if (gameIndex !== -1) {
        games.value[gameIndex].status = isActive ? 'active' : 'inactive'
      }
    }
  } catch (error) {
    console.error('Status change error:', error)
    // Revert the switch if there was an error
    // ElMessage handles the error message in the composable
  }
}

// Helper functions for server status
const getServerStatusIconClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-500'
    case 'maintenance':
      return 'bg-yellow-500'
    case 'inactive':
    default:
      return 'bg-red-500'
  }
}

const getServerStatusTextClass = (status) => {
  switch (status) {
    case 'active':
      return 'text-green-600'
    case 'maintenance':
      return 'text-yellow-600'
    case 'inactive':
    default:
      return 'text-red-600'
  }
}

const getServerStatusLabel = (status) => {
  switch (status) {
    case 'active':
      return 'Hoạt động'
    case 'maintenance':
      return 'Bảo trì'
    case 'inactive':
    default:
      return 'Không hoạt động'
  }
}

const showMetaData = (metaData, serverName = '') => {
  selectedMetaData.value = metaData
  selectedServerName.value = serverName
  showMetaModal.value = true
  
  // Force update
  setTimeout(() => {
  }, 100)
}



const openAddModal = () => {
  editingGame.value = null
  showModal.value = true
  
  // Force update
  setTimeout(() => {
  }, 100)
}

const openEditModal = (game) => {
  editingGame.value = { ...game }
  showModal.value = true
  
  // Force update
  setTimeout(() => {
  }, 100)
}

const closeModal = () => {
  showModal.value = false
  editingGame.value = null
}

const handleSaveGame = async (gameData) => {
  try {
    let result
    
    if (editingGame.value) {
      // Update existing game
      result = await updateGame(editingGame.value.id, gameData)
    } else {
      // Add new game
      result = await createGame(gameData)
    }

    if (result) {
      closeModal()
      // Reload games data
      await loadGamesData()
    }
  } catch (error) {
    console.error('Save game error:', error)
    // ElMessage handles the error message in the composable
  }
}

const getGameInitials = (name) => {
  if (!name) return 'G'
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const handleImageError = (event) => {
  // Hide broken image when error occurs
  event.target.style.display = 'none'
}

const handleEditMetaData = (metaData) => {
  // Handle editing metadata - you could open another modal or update the existing one
  ElMessage.info('Chức năng chỉnh sửa metadata đang được phát triển')
}

const confirmDelete = async (game) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa game "${game?.name}"?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const result = await deleteGame(game.id)
    if (result) {
      // Reload games data
      await loadGamesData()
    }
  } catch {
    // User cancelled
  }
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(async () => {
    currentPage.value = 1 // Reset to first page when searching
    await loadGamesData()
    ensureExpandFunctionality()
  }, 300)
})

// Watch pagination changes
watch([currentPage, pageSize], () => {
  ensureExpandFunctionality()
})

// Watch games data changes
watch(games, () => {
  ensureExpandFunctionality()
}, { deep: true })


// Lifecycle
onMounted(async () => {
  await loadGamesData()
})

// Function to ensure expand functionality works
const ensureExpandFunctionality = () => {
  setTimeout(() => {
    const tableEl = document.querySelector('.games-table')
    if (tableEl) {
      // Simple override for expand icons - remove borders and add rotation
      const expandIcons = tableEl.querySelectorAll('.el-table__expand-icon')
      expandIcons.forEach(icon => {
        icon.style.border = 'none'
        icon.style.background = 'transparent'
        icon.style.padding = '4px'
        icon.style.borderRadius = '0'
        icon.style.transition = 'transform 0.2s ease'
        icon.style.width = '24px'
        icon.style.height = '24px'
      })
      
      // Update icon rotation based on current expanded state
      const expandedRowKeys = Array.from(expandedRows.value)
      expandedRowKeys.forEach(rowId => {
        const rowEl = tableEl.querySelector(`[data-row-key="${rowId}"]`)
        if (rowEl) {
          rowEl.classList.add('expanded')
        }
      })
    }
  }, 100)
}
</script>

<style lang="scss" scoped>
/* Enhanced table styling */
:deep(.games-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.games-table .even-row) {
  background-color: var(--el-bg-color);
}

:deep(.games-table .odd-row) {
  background-color: var(--el-fill-color-light);
}

:deep(.games-table .even-row:hover),
:deep(.games-table .odd-row:hover) {
  background-color: var(--el-fill-color) !important;
}

:deep(.games-table .el-table__header-wrapper) {
  background-color: var(--el-bg-color-page);
}

:deep(.games-table .el-table__row) {
  transition: background-color 0.2s ease;
  height: 56px !important;
  min-height: 56px !important;
}

:deep(.games-table .el-table__cell) {
  border-color: var(--el-border-color-lighter);
  vertical-align: middle;
}

/* Ensure all table headers have consistent styling */
:deep(.games-table .el-table__header .el-table__cell) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 14px !important;
  padding: 12px !important;
  border-bottom: 1px solid var(--el-border-color-light) !important;
  text-align: center !important;
  height: 48px !important;
}

/* Ensure all table body cells have consistent styling */
:deep(.games-table .el-table__body .el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  padding: 12px !important;
  font-size: 14px !important;
  height: 56px !important;
  vertical-align: middle !important;
}

/* Clean expand icon styling - no borders, with rotation animation */
:deep(.games-table .el-table__expand-icon) {
  border: none !important;
  background: transparent !important;
  padding: 4px !important;
  border-radius: 0 !important;
  transition: transform 0.2s ease !important;
  color: #6b7280 !important;
  cursor: pointer !important;
  width: 24px !important;
  height: 24px !important;
}

:deep(.games-table .el-table__expand-icon:hover) {
  color: #409eff !important;
}

/* Sync button styling in expanded content */
:deep(.expanded-content .sync-button) {
  min-width: 120px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

:deep(.expanded-content .sync-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

:deep(.expanded-content .sync-button:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Icon rotation when expanded - horizontal to vertical */
:deep(.games-table .el-table__row.expanded .el-table__expand-icon) {
  transform: rotate(90deg) !important;
}

/* Add class to track expanded rows */
:deep(.games-table .el-table__row[aria-expanded="true"] .el-table__expand-icon) {
  transform: rotate(90deg) !important;
}

/* Expanded content styling */
.expanded-content {
  border-left: 3px solid #409eff;
  background-color: #f8faff;
  margin: 0 -12px;
  padding: 16px;
}

.dark .expanded-content {
  background-color: #1e293b;
  border-left-color: #409eff;
}

/* Server Cards Styling */
.server-card {
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.dark .server-card {
  border-color: #374151;
}

.server-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .server-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Table empty state styling */
:deep(.games-table .el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

:deep(.games-table .el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode support */
.dark :deep(.games-table .el-table__empty-block) {
  background-color: #1e293b;
}

.dark :deep(.games-table .el-table__empty-text) {
  color: #94a3b8;
}

/* Responsive improvements */
:deep(.games-table .el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.games-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Switch styling */
:deep(.games-table .el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Game thumbnail styling */
.game-thumbnail {
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.game-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar for meta data modal */
pre::-webkit-scrollbar {
  width: 6px;
}

pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>